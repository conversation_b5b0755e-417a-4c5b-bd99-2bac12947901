Dockerfile:8
--------------------
   7 |     # 安装系统依赖
   8 | >>> RUN apt-get update && apt-get install -y \
   9 | >>>     # 基本构建工具
  10 | >>>     python3 \
  11 | >>>     make \
  12 | >>>     g++ \
  13 | >>>     # Playwright 依赖
  14 | >>>     chromium \
  15 | >>>     nss \
  16 | >>>     freetype \
  17 | >>>     freetype-dev \
  18 | >>>     harfbuzz \
  19 | >>>     ca-certificates \
  20 | >>>     ttf-freefont \
  21 | >>>     # 其他依赖
  22 | >>>     gcompat
  23 |     
--------------------
ERROR: failed to build: failed to solve: process "/bin/sh -c apt-get update && apt-get install -y     python3     make     g++     chromium     nss     freetype     freetype-dev     harfbuzz     ca-certificates     ttf-freefont     gcompat" did not complete successfully: exit code: 100
Error: buildx failed with: ERROR: failed to build: failed to solve: process "/bin/sh -c apt-get update && apt-get install -y     python3     make     g++     chromium     nss     freetype     freetype-dev     harfbuzz     ca-certificates     ttf-freefont     gcompat" did not complete successfully: exit code: 100

# 设置 Playwright 的环境变量
ENV PLAYWRIGHT_BROWSERS_PATH=/usr/bin
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
ENV PLAYWRIGHT_EXECUTABLE_PATH=/usr/bin/chromium-browser
ENV PLAYWRIGHT_SKIP_BROWSER_VALIDATION=1

# 创建用户和基础目录结构（在安装依赖之前）
RUN groupadd -g 1001 nodejs && \
    useradd -u 1001 -g nodejs -m -d /home/<USER>
    mkdir -p /app/screenshots /app/logs && \
    chown -R nodejs:nodejs /home/<USER>/app && \
    chmod -R 777 /app/screenshots /app/logs /home/<USER>

# 复制依赖文件并安装
COPY package*.json  ./
RUN npm ci --only=production

USER nodejs
# 切换到非 root 用户
ENV HOME=/home/<USER>

# 复制应用文件（变化频率最高，放在最后）
COPY --chown=nodejs:nodejs dist/ ./dist/
COPY --chown=nodejs:nodejs public/ ./public/

# 声明容器要暴露的端口
EXPOSE 7860
ENV PORT=7860

# 启动应用
CMD ["node", "dist/index.js"]